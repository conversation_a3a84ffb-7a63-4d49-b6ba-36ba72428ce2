# Generic PDF Heading Extractor

## Approach
This project implements a robust, document-agnostic PDF heading extraction system designed to work across a wide variety of PDF formats and layouts. The approach is based on dynamic analysis of text blocks extracted from PDFs, using features such as font size, font style (bold/italic), position, and content patterns to identify document titles and hierarchical headings (H1, H2, H3, H4). The system avoids hardcoded document-specific rules, instead relying on statistical and pattern-based heuristics to generalize across different document types.

Key steps include:
- Extracting text blocks and their formatting information from each PDF page.
- Analyzing font statistics to dynamically determine thresholds for heading detection.
- Scoring each text block for heading likelihood using font size, boldness, position, and content patterns.
- Classifying headings into levels (H1-H4) based on their features.
- Outputting a structured JSON with the document title and outline.

## Models and Libraries Used
- **PyMuPDF (fitz):** For parsing PDF files and extracting text blocks with formatting information.
- **Python Standard Library:** Modules such as `re`, `statistics`, `json`, `logging`, and `collections` are used for pattern matching, statistical analysis, and data handling.
- **Custom Data Structures:** The `TextBlock` class (defined in `data_structures.py`) is used to represent extracted text blocks with relevant attributes.

No machine learning models are used; the solution is based on rule-based and statistical heuristics for maximum generalizability and transparency.

## How to Build and Run

### Prerequisites
- Python 3.8 or higher
- [PyMuPDF](https://pymupdf.readthedocs.io/en/latest/) (install via pip)

### Installation
1. Clone or download the repository.
2. Install required Python packages:
   ```powershell
   pip install -r requirements.txt
   ```

### Usage
1. Place your PDF files in the `input/` directory.
2. Run the main script:
   ```powershell
   python main_hackathon_optimized.py
   ```
3. Extracted results will be saved as JSON files in the `output/` directory, with each file containing the detected title and outline for the corresponding PDF.

### Output Format
Each output JSON file contains:
- `title`: The detected document title (string)
- `outline`: A list of heading entries, each with:
  - `level`: Heading level (H1, H2, H3, H4)
  - `text`: Heading text
  - `page`: Page number where the heading appears

### Notes
- The solution is optimized for compatibility with hackathon ground truth examples but is designed to generalize to unseen documents.
- For evaluation, use the provided `ground_truth_analyzer.py` script to compare outputs with expected results.
